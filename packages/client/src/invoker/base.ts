import {ActivationContext, Information, SystemInfoParsed} from '@comate/plugin-shared-internals';
import {PluginConfig} from '../internals/config.js';
import {InformationRetriever} from '../internals/information.js';
import {SuppotingLlm} from '../internals/llm.js';
import {Permission} from '../internals/permission.js';
import {MarkdownRender} from '../internals/render.js';
import {User} from '../internals/user.js';
import {SessionLogger} from '../internals/logger.js';
import {ClientRegistry, ProviderCapability} from '../registry.js';
import {ProviderInit} from '../providers/base.js';
import {EngineSession} from '../channel.js';

export interface InvokeContext {
    session: EngineSession;
    capability: ProviderCapability;
    pluginName: string;
    systemInfo: SystemInfoParsed;
    activationContext: ActivationContext;
    informationList: Information[];
    isRegenerated?: boolean;
    sessionUuid?: string;
}

export abstract class InvokerBase {
    protected readonly registry: ClientRegistry;
    protected readonly pluginName: string;

    constructor(pluginName: string, registry: ClientRegistry) {
        this.pluginName = pluginName;
        this.registry = registry;
    }

    protected createProviderInstance(context: InvokeContext) {
        const {
            pluginName,
            systemInfo,
            capability,
            activationContext,
            session,
            informationList,
            isRegenerated,
            sessionUuid,
        } = context;
        const logger = new SessionLogger(session);
        const permission = new Permission(pluginName, session, logger);
        const init: ProviderInit = {
            logger,
            permission,
            cwd: systemInfo.cwd,
            config: new PluginConfig(pluginName, session, logger),
            currentUser: new User(systemInfo.userId, systemInfo.userDetail, permission),
            currentContext: activationContext,
            retriever: new InformationRetriever(
                systemInfo.cwd,
                systemInfo.repoId || '',
                informationList,
                pluginName,
                session,
                logger
            ),
            render: new MarkdownRender(),
            llm: new SuppotingLlm(pluginName, session, logger, capability.name),
            isRegenerated,
            sessionUuid,
        };

        const provider = new capability.Provider(init);
        session.log(
            'system',
            this.constructor.name,
            'ProviderCreate',
            {provider: capability.Provider.name}
        );
        return provider;
    }
}

import {PermissionType} from '@comate/plugin-shared-internals';
import {PluginConfig} from '../internals/config.js';
import {ActivationContext} from '../internals/context.js';
import {FileSystem, FullDiskFileSystem, RestrictedFileSystem} from '../internals/fileSystem.js';
import {InformationRetriever} from '../internals/information.js';
import {SuppotingLlm} from '../internals/llm.js';
import {Permission} from '../internals/permission.js';
import {MarkdownRender} from '../internals/render.js';
import {User} from '../internals/user.js';
import {Logger} from '../internals/logger.js';
import {isInternal} from '../utils/checkVersion.js';

export interface ProviderInit {
    cwd: string;
    logger: Logger;
    permission: Permission;
    config: PluginConfig;
    currentUser: User;
    currentContext: ActivationContext;
    retriever: InformationRetriever;
    render: MarkdownRender;
    llm: SuppotingLlm;
    isRegenerated?: boolean;
    sessionUuid?: string;
}

/**
 * 能力提供器基类
 */
export abstract class BaseProvider {
    protected commandContext: ActivationContext | null = null;
    protected readonly logger: Logger;
    protected readonly permission: Permission;
    protected readonly config: PluginConfig;
    protected readonly currentUser: User;
    protected readonly currentContext: ActivationContext;
    protected readonly retriever: InformationRetriever;
    protected readonly render: MarkdownRender;
    protected readonly llm: SuppotingLlm;
    protected readonly isRegenerated: boolean | undefined;
    protected readonly sessionUuid: string | undefined;
    readonly #workspaceFileSystem: FileSystem | null;
    readonly #fullDiskFileSystem: FileSystem;

    constructor(init: ProviderInit) {
        this.logger = init.logger.for(this.constructor.name);
        this.#workspaceFileSystem = init.cwd ? new RestrictedFileSystem(init.cwd, init.logger) : null;
        this.#fullDiskFileSystem = new FullDiskFileSystem(init.logger);
        this.permission = init.permission;
        this.config = init.config;
        this.currentUser = init.currentUser;
        this.currentContext = init.currentContext;
        this.retriever = init.retriever;
        this.render = init.render;
        this.llm = init.llm;
        this.isRegenerated = init.isRegenerated;
        this.sessionUuid = init.sessionUuid;
    }

    setCommandContext(context: ActivationContext) {
        this.commandContext = context;
    }

    /**
     * 申请当前工作台的文件系统访问权限
     *
     * @returns 用户同意授权时返回一个`FileSystem`对象，仅允许访问当前工作台内的文件，拒绝则返回`false`，没有工作台时返回`null`。
     */
    protected async requestWorkspaceFileSystem(): Promise<FileSystem | null | false> {
        if (this.#workspaceFileSystem) {
            // 厂内直接授权
            if (isInternal) {
                return this.#workspaceFileSystem;
            }
            const granted = await this.permission.requestPermission(PermissionType.WorkspaceFileSystem);
            return granted ? this.#workspaceFileSystem : false;
        }
        return null;
    }

    /**
     * 申请全盘文件系统访问权限
     *
     * @returns 用户同意授权时返回一个`FileSystem`对象，可以访问完整的文件系统，拒绝则返回`false`
     */
    protected async requestFullDiskFileSystem(): Promise<FileSystem | false> {
        const granted = await this.permission.requestPermission(PermissionType.FullDiskFileSystem);
        return granted ? this.#fullDiskFileSystem : false;
    }

    /**
     * 申请安全上传文件权限
     *
     * @returns 返回当前企业管理员配置的权限
     */
    protected async requestCodeSecurity(): Promise<boolean> {
        const granted = await this.permission.requestPermission(PermissionType.CodeSecurity);
        return granted;
    }

    protected disableLogging(disableType: 'llm' | 'fs' | 'retriever') {
        if (disableType === 'fs') {
            this.#workspaceFileSystem?.disableLogging();
            this.#fullDiskFileSystem.disableLogging();
        }
        if (disableType === 'llm') {
            this.llm.disableLogging();
        }
        if (disableType === 'retriever') {
            this.retriever.disableLogging();
        }
    }
}

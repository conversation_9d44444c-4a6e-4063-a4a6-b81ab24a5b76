/* eslint-disable complexity */
import crypto from 'node:crypto';
import path from 'node:path';
import {
    KnowledgeSetBase,
    ACTION_SCAN_QUERY,
    ACTION_CHAT_QUERY as INTERNAL_ACTION_CHAT_QUERY,
    ACTION_CUSTOM_COMMAND as INTERNAL_ACTION_CUSTOM_COMMAND,
    ACTION_COMATE_PLUS_CHAT_QUERY,
    ACTION_COMATE_PLUS_CUSTOM_COMMAND,
    ACTION_COMATE_PLUS_DRAW_CHAT_FAIL,
    ACTION_COMATE_PLUS_DRAW_CHAT_FINISH,
    ACTION_COMATE_PLUS_DRAW_CHAT_UPDATE,
    ACTION_COMATE_PLUS_WEBVIEW_INIT_DATA,
    ACTION_UPDATE_ENGINE_CONFIG,
    canShowToUser,
    ACTION_QUERY_SELECTOR,
    SystemInfoParsed,
    ACTION_COMATE_PLUS_QUERY_SELECTOR,
    CustomCommandPayload,
    ACTION_COMATE_PLUS_DIAGNOSTIC_SCAN,
    setApiHost,
    ACTION_COMATE_PLUS_SECTION_CHAT_UPDATE,
    isS<PERSON><PERSON>Chunk,
    ACTION_GENERATE_MESSAGE,
    ACTION_GENERATE_MESSAGE_REPORT,
    ACTION_BRANCH_CHANGE,
    ACTION_COMATE_SMART_APPLY,
    ACTION_CHAT_SESSION_LIST,
    ACTION_CHAT_SESSION_SAVE,
    ACTION_CHAT_SESSION_DELETE,
    ACTION_CHAT_SESSION_FIND,
    VirtualEditor,
    ACTION_MOCK_VIRTUAL_EDITOR_EVENT,
    ACTION_V8_SNAP_SHOT,
    ACTION_COMATE_ADD_CACHE,
    ACTION_COMATE_GET_CACHE,
    ACTION_PROMPTTEMPLATE_LIST,
    ACTION_PROMPTTEMPLATE_CREATE,
    ACTION_PROMPTTEMPLATE_DELETE,
    ACTION_PROMPTTEMPLATE_UPDATE,
    ACTION_SCAN_CACHE_EXISTS,
    ACTION_COMATE_PLUS_CHAT_CANCEL,
} from '@comate/plugin-shared-internals';
import {LoggerRoleInstance} from '../Logger.js';
import {getHeapSnapshot} from '../getHeapSnapshot.js';
import {Mediator} from '../Mediator.js';
import {MessageEvent} from '../PluginChannel/index.js';
import {isJetbrains} from '../../utils/checkIDE.js';
import {isSaaS} from '../../utils/isSaaS.js';
import {apiStreamPostSmartApply} from '../SmartApply/api.js';
import {ComatePlusChatQueryPayload, PromptTemplateCreatePayload, PromptTemplateList} from '../../types/ideListener.js';

interface QueryParams {
    scanId: string;
    scanType: string;
    // TODO: messageId 在client的类型声明中是个string，不一致会导致问题
    messageId: number;
    pluginName: string;
    capability: string;
    query: string;
    data?: any;
    customCommandPayload?: CustomCommandPayload;
    knowledgeList: KnowledgeSetBase[];
    sessionUuid: string;
    context: {
        query: string;
        selectedCode: string;
        selectedRange: [{line: number, character: number}, {line: number, character: number}] | [];
        activeFileContent: string;
        activeFileLineContent: string;
        activeFilePath: string;
        activeFileName: string;
        activeFileLanguage: string;
    };
    systemInfo: SystemInfoParsed;
    agentPayload: any;
    chatCancleToken?: string;
    isRegenerated?: boolean;
}

interface KnowledgeList {
    id: string;
    name: string;
    type: 'NORMAL' | 'SYSTEM' | 'FILE' | 'TEMP';
}

interface AgentList {
    name: string;
    displayName?: string;
    description?: string;
    icon?: string;
}
interface AgentListWithId extends AgentList {
    id: string;
}

interface CommandListWithId {
    displayName?: string; // 中文名称
    name: string; // 英文名称
    owner: AgentList;
    type?: string; // 'Skill' | 'Prompt' | 'Fallback'
    placeholder?: string;
    defaultUserMessage?: string;
    displayTag?: string;
}

const uniqueArray = (array: any[], id: any) => array.filter((v, i, a) => a.findIndex(t => (t[id] === v[id])) === i);

export async function sendMessageToIde(logger: LoggerRoleInstance, mediator: Mediator, message?: MessageEvent) {
    if (message) {
        const {data: {payload}} = message;
        logger.verbose('message:', message);
        const chunk = payload.chunk;
        // TODO 这块是临时处理
        // ! 需要兼容最新的通信格式
        if (typeof chunk === 'object' && 'command' in chunk) {
            if (chunk.command === 'fail') {
                await mediator.sendToIde(ACTION_COMATE_PLUS_DRAW_CHAT_FAIL, {
                    ...payload,
                    messageId: payload.messageId || payload.taskId,
                    chunk: {
                        // @ts-expect-error
                        ...payload.chunk,
                        content: Array.isArray(chunk.content)
                            ? chunk
                                .content
                                .map(v => (typeof v === 'string' ? v : v.children))
                                .join('')
                            : chunk.content,
                    },
                });
                return {
                    status: 'error',
                    payload: chunk.content,
                };
            }
            else if (chunk.command === 'draw') {
                try {
                    if (Array.isArray(chunk.content)) {
                        let content: string | any[] = chunk.content;
                        const isJsx = chunk.content.find(content => typeof content !== 'string');
                        if (!isJsx) {
                            content = chunk.content.join('');
                        }
                        await mediator.sendToIde(ACTION_COMATE_PLUS_DRAW_CHAT_UPDATE, {
                            ...payload,
                            messageId: payload.messageId || payload.taskId,
                            chunk: {
                                // @ts-expect-error
                                ...payload.chunk,
                                content,
                            },
                        });
                    }
                }
                catch (e) {
                    logger.error('send to ide draw update error', e);
                }
            }
            else if (isSectionChunk(chunk)) {
                try {
                    if (chunk.sections.dynamicSections.length || chunk.sections.dynamicFooterSections.length) {
                        await mediator.sendToIde(ACTION_COMATE_PLUS_SECTION_CHAT_UPDATE, {
                            ...payload,
                            messageId: payload.messageId || payload.taskId,
                            chunk: {
                                // @ts-expect-error
                                ...payload.chunk,
                            },
                        });
                    }
                }
                catch (e) {
                    logger.error('send to ide section update error', e);
                }
            }
        }
        else {
            await mediator.sendToIde(ACTION_COMATE_PLUS_DRAW_CHAT_UPDATE, {
                ...payload,
                messageId: payload.messageId || payload.taskId,
                chunk: {
                    // @ts-expect-error
                    ...payload.chunk,
                    content: chunk
                        // @ts-expect-error
                        .content
                        // @ts-expect-error
                        .map(v => (typeof v === 'string' ? v : v.children))
                        .join(''),
                },
            });
        }
    }
}

/* eslint-disable max-depth */
export async function ideListener(
    method: string,
    params: QueryParams,
    mediator: Mediator,
    logger: LoggerRoleInstance,
    virtualEditor: VirtualEditor
) {
    switch (method) {
        case ACTION_COMATE_PLUS_CHAT_QUERY: {
            try {
                const userDetail = mediator.userDetail();
                const capabilityList = mediator
                    .getPluginCapabilityInfo();

                // 显示默认消息内容时，传给插件的query值依然是空串 会影响意图识别等逻辑，一定要传空的
                let query = params.context.query ?? '';
                if (params.pluginName !== 'PromptTemplate') {
                    const capability = capabilityList.find(capability => capability.name === params.capability);
                    if (params.context.query === capability?.defaultUserMessage) {
                        query = '';
                    }
                }

                const payload: ComatePlusChatQueryPayload = {
                    isRegenerated: params.isRegenerated,
                    pluginName: params.pluginName,
                    sessionUuid: params.sessionUuid,
                    input: {
                        messageId: params.messageId,
                        capability: params.capability,
                        query: params.query,
                        data: params.data,
                        informationList: params.knowledgeList ?? [],
                    },
                    // TODO:context可能IDE不会传，需要自己获取
                    context: {
                        query,
                        selectedCode: params.context.selectedCode ?? '',
                        // selectedCodes: [], // LS 无法获取selectedCode，需要IDE提供
                        selectedRange: params.context.selectedRange ?? [],
                        activeFileContent: params.context.activeFileContent ?? '',
                        activeFileLineContent: params.context.activeFileLineContent ?? '',
                        activeFilePath: params.context.activeFilePath ?? '',
                        activeFileName: params.context.activeFileName ?? '',
                        activeFileLanguage: params.context.activeFileLanguage ?? '',
                    },
                    systemInfo: {
                        ...params.systemInfo,
                        userDetail: {
                            name: userDetail.name,
                            license: userDetail.license,
                            displayName: userDetail.displayName,
                            email: userDetail.email,
                        },
                        userId: userDetail.uid,
                    },
                };

                if (params.pluginName === 'PromptTemplate') {
                    logger.logUploader?.logUserAction({
                        category: 'promptTemplate',
                        action: 'chat',
                        content: 'promptTemplate chat query',
                    });

                    for await (
                        const message of mediator.promptTemplateChat(
                            params.capability,
                            payload
                        )
                    ) {
                        logger.info('PromptTemplate message', message);
                        await sendMessageToIde(logger, mediator, message);
                    }
                }
                else {
                    const sessionId = crypto.randomUUID();
                    // 很奇怪的一个问题，对接jetbrains 下面的async iterator 不会结束，导致收不到finish的消息
                    for await (
                        const message of mediator.sendToPlugin(
                            sessionId,
                            {action: INTERNAL_ACTION_CHAT_QUERY, payload}
                        )
                    ) {
                        logger.info('message messagemessagemessage', message);
                        // 非 Jetbrains 在这里发送
                        !isJetbrains && await sendMessageToIde(logger, mediator, message);
                    }
                }

                await mediator.sendToIde(ACTION_COMATE_PLUS_DRAW_CHAT_FINISH, {messageId: params.messageId});
                return {
                    status: 'success',
                    payload: 'ok',
                };
            }
            catch (error) {
                logger.error(
                    'ERROR GET',
                    ACTION_COMATE_PLUS_CHAT_QUERY,
                    (error instanceof Error) ? error.toString() : error
                );
                return {
                    status: 'error',
                    payload: 'not ok',
                };
            }
        }
        case ACTION_COMATE_PLUS_CHAT_CANCEL: {
            try {
                const userDetail = mediator.userDetail();
                const payload: ComatePlusChatQueryPayload = {
                    pluginName: params.pluginName,
                    input: {
                        messageId: params.messageId,
                        capability: '',
                        query: '',
                        data: {
                            chatCancleToken: params.chatCancleToken,
                        },
                        informationList: [],
                    },
                    context: {
                        query: '',
                        selectedCode: '',
                        selectedRange: [],
                        activeFileContent: '',
                        activeFileLineContent: '',
                        activeFilePath: '',
                        activeFileName: '',
                        activeFileLanguage: '',
                    },
                    systemInfo: {
                        cwd: '',
                        userDetail: {
                            name: userDetail.name,
                            license: userDetail.license,
                            displayName: userDetail.displayName,
                            email: userDetail.email,
                        },
                        userId: userDetail.uid,
                    },
                };
                const sessionId = crypto.randomUUID();
                for await (
                    const message of mediator.sendToPlugin(
                        sessionId,
                        {action: INTERNAL_ACTION_CHAT_QUERY, payload}
                    )
                ) {
                    logger.info('cancle message', message);
                }
                return {
                    status: 'success',
                    payload: 'ok',
                };
            }
            catch (error) {
                logger.error(
                    'ERROR GET',
                    ACTION_COMATE_PLUS_CHAT_CANCEL,
                    (error instanceof Error) ? error.toString() : error
                );
                return {
                    status: 'error',
                    payload: 'not ok',
                };
            }
        }
        case ACTION_COMATE_PLUS_CUSTOM_COMMAND: {
            try {
                const sessionId = crypto.randomUUID();

                // 很奇怪的一个问题，对接jetbrains 下面的async iterator 不会结束，导致收不到finish的消息
                for await (
                    const message of mediator.sendToPlugin(
                        sessionId,
                        {
                            action: INTERNAL_ACTION_CUSTOM_COMMAND,
                            payload: {
                                ...params.customCommandPayload,
                                messageId: params.messageId,
                                commandContext: params.context,
                            },
                        }
                    )
                ) {
                    if (message) {
                        // 补充一个messageId, taskId表示按钮所在消息ID，messageId表示要回复的消息ID
                        message.data.payload.messageId = params.messageId;
                    }
                    // 非 Jetbrains 在这里发送
                    !isJetbrains && await sendMessageToIde(logger, mediator, message);
                }
                await mediator.sendToIde(ACTION_COMATE_PLUS_DRAW_CHAT_FINISH, {messageId: params.messageId});
                return {
                    status: 'success',
                    payload: 'ok',
                };
            }
            catch (error) {
                logger.error(
                    'ERROR GET',
                    ACTION_COMATE_PLUS_CUSTOM_COMMAND,
                    (error instanceof Error) ? error.toString() : error
                );
                return {
                    status: 'error',
                    payload: 'not ok',
                };
            }
        }
        // 执行 WEBVIEW_INIT_DATA 的逻辑的时候可能会报 Cannot read properties of undefined (reading 'getCurrentUserKnowledgeSet')
        // 原因是engine初始化的流程依赖于ide能通信，且要获取用户详细信息，整个过程是个异步。在未完成完整初始化流程时，可能会收到ide的 WEBVIEW_INIT_DATA 的请求
        case ACTION_COMATE_PLUS_WEBVIEW_INIT_DATA: {
            let agentListWithId: AgentListWithId[] = [];
            let commandListWithId: CommandListWithId[] = [];
            let knowledgeList: KnowledgeList[] = [];
            let promptTemplateList: PromptTemplateList[] = [];
            let mergedConfigs: Record<string, any> = {};

            try {
                knowledgeList = (await mediator.pluginConfigApi.getCurrentUserKnowledgeSet())
                    .map(v => ({
                        id: v.uuid,
                        name: v.name,
                        type: v.type,
                        description: v.description,
                        retrievalType: v.fileContentType || 'TEXT',
                    }));
            }
            catch (error) {
                knowledgeList = [];
                logger.error(
                    'ERROR GET',
                    ACTION_COMATE_PLUS_WEBVIEW_INIT_DATA,
                    (error instanceof Error) ? error.toString() : error
                );
            }

            try {
                promptTemplateList = await mediator.promptTemplateService.getAll();
            }
            catch (error) {
                promptTemplateList = [];
                logger.error(
                    'ERROR GET',
                    ACTION_COMATE_PLUS_WEBVIEW_INIT_DATA,
                    (error instanceof Error) ? error.toString() : error
                );
            }

            try {
                const userDetail = mediator.userDetail();
                mergedConfigs = await mediator.getPluginConfigAll();

                const enablePluginNames = Object.keys(mergedConfigs).filter(v => {
                    // saas 需要过滤 onlyBusinesses 对企业用户启用
                    if (mergedConfigs[v]?.meta?.onlyBusinesses && isSaaS()) {
                        // 对普通用户和未开启安全功能的企业用户不启用
                        if (
                            !userDetail.enterpriseConfig?.enterprise || !userDetail.enterpriseConfig?.enableCodeSecurity
                        ) {
                            mergedConfigs[v].enabled = false;
                        }
                    }
                    return mergedConfigs[v]?.enabled;
                });
                const capabilityList = mediator
                    .getPluginCapabilityInfo()
                    .filter(v => enablePluginNames.includes(v.owner.name));
                agentListWithId = uniqueArray(
                    capabilityList.map((item: any) => item.owner),
                    'name'
                )
                    .map(item => {
                        // 为 agent 新增一个id，用以唯一标识
                        return {...item, id: item.name};
                    });
                commandListWithId = capabilityList
                    .filter(v => canShowToUser(v.type))
                    .map(item => {
                        // 为 command 新增一个id，用以唯一标识
                        return {...item, id: item.name + '@' + item.owner.name};
                    });
            }
            catch (error) {
                logger.error(
                    'ERROR GET',
                    ACTION_COMATE_PLUS_WEBVIEW_INIT_DATA,
                    (error instanceof Error) ? error.toString() : error
                );
                return {
                    status: 'error',
                    payload: [[[], [], [], []], {}],
                };
            }

            return {
                status: 'success',
                payload: [
                    [
                        agentListWithId,
                        commandListWithId,
                        knowledgeList,
                        promptTemplateList,
                    ],
                    mergedConfigs,
                ],
            };
        }
        case ACTION_QUERY_SELECTOR: {
            try {
                const userDetail = mediator.userDetail();
                const capabilities = await mediator.getSuggestCapabilitiesByMatch({
                    context: params.context,
                    systemInfo: {
                        ...params.systemInfo,
                        userDetail: {
                            license: userDetail.license,
                            name: userDetail.name,
                            displayName: userDetail.displayName,
                            email: userDetail.email,
                        },
                        userId: userDetail.uid,
                    },
                });
                mediator.sendToIde(ACTION_COMATE_PLUS_QUERY_SELECTOR, capabilities);
                break;
            }
            catch (error) {
                logger.error('ERROR GET', ACTION_QUERY_SELECTOR, (error instanceof Error) ? error.toString() : error);
                return {
                    status: 'error',
                    payload: ACTION_QUERY_SELECTOR,
                };
            }
        }
        case ACTION_COMATE_PLUS_DIAGNOSTIC_SCAN: {
            try {
                const userDetail = mediator.userDetail();
                const absoluteActiveFilePath = path.isAbsolute(params.context.activeFilePath)
                    ? params.context.activeFilePath
                    : path.join(params.systemInfo.cwd, params.context.activeFilePath);
                const result = mediator.diagnosticCache.get({
                    pluginName: params.pluginName,
                    capabilityName: params.capability,
                    systemInfo: {
                        ...params.systemInfo,
                        userDetail: {
                            name: userDetail.name,
                            license: userDetail.license,
                            displayName: userDetail.displayName,
                            email: userDetail.email,
                        },
                        userId: userDetail.uid,
                    },
                    context: {
                        ...params.context,
                        activeFilePath: absoluteActiveFilePath,
                    },
                });
                return result;
            }
            catch (error) {
                logger.error(
                    'ERROR GET',
                    ACTION_COMATE_PLUS_DIAGNOSTIC_SCAN,
                    (error instanceof Error) ? error.toString() : error
                );
                return {};
            }
        }
        case ACTION_SCAN_QUERY: {
            mediator.launchSendToPlugin(
                params.scanId,
                {
                    action: ACTION_SCAN_QUERY,
                    payload: params,
                }
            );
            break;
        }
        case ACTION_SCAN_CACHE_EXISTS: {
            const result = mediator.diagnosticCache.hasDiagnostics();
            return result;
        }
        case ACTION_UPDATE_ENGINE_CONFIG: {
            const config = params as unknown as {
                apiHost: string;
            };
            setApiHost(config.apiHost);
            break;
        }
        case ACTION_GENERATE_MESSAGE: {
            const {paths, cwd} = params.data;
            const message = await mediator.messageGenerate.generateCommitMessage(paths, cwd);
            return message;
        }
        case ACTION_GENERATE_MESSAGE_REPORT: {
            const {message, cwd} = params.data;
            const response = await mediator.messageGenerate.report(message, cwd);
            return response;
        }
        case ACTION_BRANCH_CHANGE: {
            await mediator.messageGenerate.getSpaceDetail();
            return;
        }
        case ACTION_COMATE_SMART_APPLY: {
            const {uuid, ...applyParams} = params as unknown as any;
            const stream = await apiStreamPostSmartApply(logger, applyParams);
            for await (const {text, error, diff, traceId, finish} of stream) {
                if (error) {
                    await mediator.sendToIde(ACTION_COMATE_SMART_APPLY, {uuid, error, traceId, finish});
                }
                else {
                    await mediator.sendToIde(ACTION_COMATE_SMART_APPLY, {uuid, text, diff, traceId, finish});
                }
            }
            return {status: 'success', payload: 'ok'};
        }
        case ACTION_CHAT_SESSION_LIST: {
            const response = await mediator.chatSessionManager.list();
            return {
                status: 'success',
                payload: response,
            };
        }
        case ACTION_CHAT_SESSION_SAVE: {
            const response = await mediator.chatSessionManager.save(params as any);
            return {
                status: 'success',
                payload: response,
            };
        }
        case ACTION_CHAT_SESSION_DELETE: {
            const response = await mediator.chatSessionManager.delete(params?.sessionUuid);
            return {
                status: 'success',
                payload: response,
            };
        }
        case ACTION_CHAT_SESSION_FIND: {
            const response = await mediator.chatSessionManager.find(params?.sessionUuid);
            return {
                status: 'success',
                payload: response,
            };
        }
        case ACTION_PROMPTTEMPLATE_LIST: {
            const promptTemplateList = await mediator.promptTemplateService.getAll();
            return {
                status: 'success',
                payload: promptTemplateList,
            };
        }
        case ACTION_PROMPTTEMPLATE_CREATE: {
            const payload: PromptTemplateCreatePayload | undefined = !params?.data?.isSystem
                ? {
                    query: params?.query,
                }
                : undefined;
            const data = await mediator.promptTemplateService.create(payload);
            if (typeof data === 'object' && data?.uri) {
                return {
                    status: 'success',
                    payload: {
                        uri: data?.uri,
                        content: data?.content,
                    },
                };
            }
            return {
                status: 'error',
                payload: data,
            };
        }
        case ACTION_PROMPTTEMPLATE_UPDATE: {
            const result = await mediator.promptTemplateService.save(params.data);
            if (!result) {
                return {
                    status: 'success',
                };
            }
            return {
                status: 'error',
                payload: result,
            };
        }
        case ACTION_PROMPTTEMPLATE_DELETE: {
            const result = await mediator.promptTemplateService.delete(params.data);
            if (!result) {
                return {
                    status: 'success',
                };
            }
            return {
                status: 'error',
                payload: result,
            };
        }
        // ACTION_COMATE_AGENT_GET_CONVERSATIONS
        case 'COMATE_AGENT_GET_CONVERSATIONS': {
            const allConversations = mediator.agentConversationManager.listAllConversations();
            return {allConversations};
        }
        case 'COMATE_AGENT_START_NEW_CHAT': {
            try {
                const {agentPayload} = typeof params?.data === 'string' ? JSON.parse(params.data) : params;
                const result = mediator.agentConversationManager.onNewMessage(agentPayload);
                return result;
            }
            catch (ex) {
                logger.error('COMATE_AGENT_START_NEW_CHAT ERROR', (ex instanceof Error) ? ex.toString() : ex);
                return {
                    status: 'error',
                    payload: ex,
                };
            }
        }
        case 'COMATE_AGENT_SET_FOREGROUND_CONVERSATION': {
            const {agentPayload} = typeof params?.data === 'string' ? JSON.parse(params.data) : params;
            mediator.agentConversationManager.setForegroundConversation(
                agentPayload.conversationId
            );
            return;
        }
        case 'COMATE_AGENT_NEW_MESSAGE': {
            const {agentPayload} = typeof params?.data === 'string' ? JSON.parse(params.data) : params;
            mediator.agentConversationManager.onNewMessage(agentPayload);
            return;
        }
        case ACTION_MOCK_VIRTUAL_EDITOR_EVENT: {
            // @ts-expect-error
            return virtualEditor[params.action](params.payload);
        }
        case ACTION_COMATE_ADD_CACHE: {
            const {key, value} = params.data;
            mediator.commonCache.setCache(key, value);
            return;
        }
        case ACTION_COMATE_GET_CACHE: {
            const cache = mediator.commonCache.getCache(params.data);
            return cache;
        }
        case VirtualEditor.event: {
            await virtualEditor.resolve(params as any);
            return;
        }
        case ACTION_V8_SNAP_SHOT: {
            try {
                const currentTimestamp = Date.now();
                const date = new Date(currentTimestamp);
                const dateString = date.toISOString();
                await getHeapSnapshot(dateString);
                return;
            }
            catch (error) {
                logger.error('ERROR GET', ACTION_V8_SNAP_SHOT, (error instanceof Error) ? error.toString() : error);
                return {status: 'error', payload: 'Failed to get snapshot'};
            }
        }
        default: {
            return {
                status: 'error',
                payload: 'unknown method',
            };
        }
    }
}
